"""Test script to verify MediaPipe GPU acceleration with RTX 3050."""

import cv2
import numpy as np
import time
import logging
from src.infrastructure.pose.mediapipe_engine import MediaPipeEngine

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_gpu_performance():
    """Test MediaPipe performance with different configurations."""
    
    # Create test frame (simulate webcam input)
    test_frame = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)
    
    print("=== GPU Acceleration Test ===")
    print(f"Test frame size: {test_frame.shape}")
    
    # Initialize MediaPipe engine
    engine = MediaPipeEngine()
    
    if not engine.initialize():
        print("❌ Failed to initialize MediaPipe engine")
        return
    
    # Warm up (first few frames are always slower)
    print("🔥 Warming up...")
    for _ in range(5):
        engine.detect_pose(test_frame)
    
    # Performance test
    print("🚀 Running performance test...")
    num_frames = 50
    start_time = time.time()
    
    successful_detections = 0
    for i in range(num_frames):
        keypoints = engine.detect_pose(test_frame)
        if keypoints:
            successful_detections += 1
        
        if (i + 1) % 10 == 0:
            elapsed = time.time() - start_time
            current_fps = (i + 1) / elapsed
            print(f"  Frame {i+1:2d}/{num_frames}: {current_fps:.1f} FPS")
    
    total_time = time.time() - start_time
    avg_fps = num_frames / total_time
    
    print(f"\n📊 Results:")
    print(f"  Total frames: {num_frames}")
    print(f"  Successful detections: {successful_detections}")
    print(f"  Total time: {total_time:.2f} seconds")
    print(f"  Average FPS: {avg_fps:.1f}")
    print(f"  Detection rate: {(successful_detections/num_frames)*100:.1f}%")
    
    # Check if we're hitting our target
    target_fps = 25
    if avg_fps >= target_fps:
        print(f"✅ SUCCESS: Achieved {avg_fps:.1f} FPS (target: {target_fps} FPS)")
        print("🎯 GPU acceleration is working!")
    else:
        print(f"⚠️  BELOW TARGET: {avg_fps:.1f} FPS (target: {target_fps} FPS)")
        if avg_fps > 15:
            print("📈 Still good performance, likely GPU-accelerated")
        else:
            print("🐌 Low performance, might be CPU-only")
    
    engine.cleanup()

def test_frame_sizes():
    """Test performance with different frame sizes."""
    print("\n=== Frame Size Optimization Test ===")
    
    frame_sizes = [
        (240, 320),   # Very small
        (480, 640),   # Medium  
        (720, 1280),  # HD
        (1080, 1920)  # Full HD
    ]
    
    engine = MediaPipeEngine()
    if not engine.initialize():
        return
    
    for height, width in frame_sizes:
        print(f"\n📐 Testing {width}x{height}...")
        test_frame = np.random.randint(0, 255, (height, width, 3), dtype=np.uint8)
        
        # Warm up
        for _ in range(3):
            engine.detect_pose(test_frame)
        
        # Test performance
        num_frames = 20
        start_time = time.time()
        
        for _ in range(num_frames):
            keypoints = engine.detect_pose(test_frame)
        
        total_time = time.time() - start_time
        fps = num_frames / total_time
        
        print(f"  {width}x{height}: {fps:.1f} FPS")
        
        if fps >= 25:
            print(f"  ✅ Excellent performance at {width}x{height}")
        elif fps >= 15:
            print(f"  ✅ Good performance at {width}x{height}")
        else:
            print(f"  ⚠️  Limited performance at {width}x{height}")
    
    engine.cleanup()

if __name__ == "__main__":
    test_gpu_performance()
    test_frame_sizes()