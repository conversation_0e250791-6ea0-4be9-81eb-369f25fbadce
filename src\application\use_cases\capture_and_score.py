"""Real-time capture and score use case for ergonomic assessment.

This module implements the core application workflow that continuously
captures video frames, detects poses, calculates ergonomic scores,
and publishes updates to the event system.
"""

import asyncio
import logging
import threading
import time
from collections import deque
from typing import Optional, Dict, Any, NamedTuple
import numpy as np

from ...core.event_bus import EventBus
from ...core.events import ScoresUpdatedEvent, CameraErrorEvent, PoseDetectionErrorEvent
from ...domain.ergonomics.entities.posture_snapshot import PostureSnapshot
from ...domain.ergonomics.services.pose_engine import PoseEngine
from ...domain.ergonomics.services.score_calculator import ErgonomicScoreCalculator
from ...infrastructure.camera.windows_camera_adapter import WindowsCameraAdapter
from .performance_monitor import PerformanceMonitor


logger = logging.getLogger(__name__)


class FrameData(NamedTuple):
    """Container for frame data with metadata."""
    frame: np.ndarray
    timestamp: float
    frame_id: int


class CaptureAndScoreUseCase:
    """Use case for continuous video capture and ergonomic scoring with async pipeline.
    
    Orchestrates high-performance real-time pipeline:
    - Async frame capture to deque buffer
    - Separate inference task for pose detection & scoring
    - Performance monitoring for ≥25 FPS target
    - Event publishing for UI updates
    """
    
    def __init__(
        self,
        camera_adapter: WindowsCameraAdapter,
        pose_engine: PoseEngine,
        score_calculator: ErgonomicScoreCalculator,
        event_bus: EventBus,
        target_fps: float = 30.0,  # Exact target as requested - minimum 30 FPS
        buffer_size: int = 2  # Streamlined buffer for maximum speed
    ):
        """Initialize the capture and score use case.
        
        Args:
            camera_adapter: Camera interface for frame capture
            pose_engine: Pose detection engine
            score_calculator: Ergonomic score calculator
            event_bus: Event system for publishing updates
            target_fps: Target processing rate in frames per second
            buffer_size: Frame buffer size for async processing
        """
        self.camera_adapter = camera_adapter
        self.pose_engine = pose_engine
        self.score_calculator = score_calculator
        self.event_bus = event_bus
        self.target_fps = target_fps
        self.buffer_size = buffer_size
        
        # Async pipeline components
        self._frame_buffer = deque(maxlen=buffer_size)
        self._buffer_lock = threading.Lock()
        self._running = False
        
        # Threading
        self._capture_thread: Optional[threading.Thread] = None
        self._inference_thread: Optional[threading.Thread] = None
        
        # Performance monitoring - optimized for stable performance
        self.performance_monitor = PerformanceMonitor(
            event_bus=event_bus,
            target_fps=target_fps,
            window_size=15,  # Smaller window for faster adaptation
            reporting_interval=2.0  # Less frequent reporting to reduce overhead
        )
        
        # Statistics
        self._frame_interval = 1.0 / target_fps
        self._frame_counter = 0
        self._stats = {
            'frames_captured': 0,
            'frames_processed': 0,
            'poses_detected': 0,
            'scores_calculated': 0,
            'buffer_overflows': 0,
            'errors': 0,
            'start_time': None
        }
    
    def start(self) -> bool:
        """Start the async capture and scoring pipeline.
        
        Returns:
            True if started successfully, False otherwise
        """
        if self._running:
            logger.warning("Capture and score pipeline already running")
            return True
        
        try:
            # Initialize camera
            if not self.camera_adapter.initialize():
                logger.error("Failed to initialize camera adapter")
                return False
            
            # Initialize pose engine
            if not self.pose_engine.initialize():
                logger.error("Failed to initialize pose engine")
                self.camera_adapter.release()
                return False
            
            # Start performance monitoring
            if not self.performance_monitor.start():
                logger.warning("Failed to start performance monitor")
            
            # Start async pipeline threads
            self._running = True
            self._stats['start_time'] = time.time()
            
            # Start capture thread (high priority)
            self._capture_thread = threading.Thread(
                target=self._capture_loop,
                daemon=True,
                name="FrameCapture"
            )
            
            # Start inference thread (lower priority)
            self._inference_thread = threading.Thread(
                target=self._inference_loop,
                daemon=True,
                name="FrameInference"
            )
            
            self._capture_thread.start()
            self._inference_thread.start()
            
            logger.info(f"Async capture and score pipeline started at {self.target_fps} FPS")
            return True
            
        except Exception as e:
            logger.error(f"Failed to start capture and score pipeline: {e}")
            self._cleanup()
            return False
    
    def stop(self) -> None:
        """Stop the async capture and scoring pipeline."""
        if not self._running:
            return
        
        logger.info("Stopping async capture and score pipeline...")
        self._running = False
        
        # Wait for threads to finish
        if self._capture_thread and self._capture_thread.is_alive():
            self._capture_thread.join(timeout=2.0)
        
        if self._inference_thread and self._inference_thread.is_alive():
            self._inference_thread.join(timeout=2.0)
        
        # Stop performance monitoring
        self.performance_monitor.stop()
        
        self._cleanup()
        self._log_final_stats()
        logger.info("Async capture and score pipeline stopped")
    
    def _capture_loop(self) -> None:
        """High-priority capture loop optimized for 25+ FPS."""
        last_frame_time = 0
        
        while self._running:
            try:
                current_time = time.time()
                
                # Minimal FPS throttling for maximum speed
                time_since_last = current_time - last_frame_time
                if time_since_last < self._frame_interval:
                    # Very short sleep for high performance
                    sleep_time = max(0.005, self._frame_interval - time_since_last)
                    time.sleep(sleep_time)
                    continue
                
                # Record frame start for performance monitoring
                frame_start = self.performance_monitor.record_frame_start()
                
                # Capture frame from camera
                frame = self.camera_adapter.read_frame()
                if frame is None:
                    # Don't spam logs for failed frames
                    continue
                
                self._stats['frames_captured'] += 1
                self._frame_counter += 1
                
                # Optimized buffer management - aggressive frame dropping for real-time performance
                try:
                    if self._buffer_lock.acquire(blocking=False):
                        try:
            # High-performance buffer management - keep buffer flowing
                            if len(self._frame_buffer) >= self.buffer_size:
                                self._frame_buffer.popleft()  # Remove oldest frame
                                # Don't count this as overflow for smooth operation
                            
                            # Add new frame
                            frame_data = FrameData(
                                frame=frame,
                                timestamp=current_time,
                                frame_id=self._frame_counter
                            )
                            self._frame_buffer.append(frame_data)
                        finally:
                            self._buffer_lock.release()
                    else:
                        # Skip frame if buffer is locked (non-blocking approach)
                        self._stats['buffer_overflows'] += 1
                except Exception:
                    # Skip frame on any buffer error
                    pass
                
                # Record capture completion
                self.performance_monitor.record_frame_end(frame_start)
                
                last_frame_time = current_time
                
            except Exception as e:
                logger.error(f"Error in capture loop: {e}")
                self._stats['errors'] += 1
                
                # Publish error event (throttled)
                if self._stats['errors'] % 10 == 1:  # Only every 10th error
                    error_event = CameraErrorEvent(
                        error_message=f"Capture loop error: {e}",
                        source="capture_and_score_use_case"
                    )
                    self.event_bus.publish(error_event)
                
                # Brief pause to prevent error spam
                time.sleep(0.05)  # Reduced from 0.1
    
    def _inference_loop(self) -> None:
        """Optimized inference loop for pose detection and scoring at 25+ FPS."""
        inference_count = 0
        last_perf_log = time.time()
        
        while self._running:
            try:
                # Non-blocking frame acquisition
                frame_data = None
                try:
                    if self._buffer_lock.acquire(blocking=False):
                        try:
                            if self._frame_buffer:
                                frame_data = self._frame_buffer.popleft()
                        finally:
                            self._buffer_lock.release()
                except Exception:
                    pass
                
                if frame_data is None:
                    # Minimal sleep for high responsiveness
                    time.sleep(0.001)
                    continue
                
                # Record inference start for performance monitoring
                inference_start = time.time()
                
                # Process frame through optimized inference pipeline
                self._process_frame_inference(frame_data, inference_start)
                
                inference_count += 1
                
                # Log performance every 50 frames for monitoring
                current_time = time.time()
                if current_time - last_perf_log > 5.0:  # Every 5 seconds
                    inference_fps = inference_count / (current_time - last_perf_log)
                    logger.debug(f"Inference FPS: {inference_fps:.1f}")
                    inference_count = 0
                    last_perf_log = current_time
                
                # No artificial sleep - run at max speed
                
            except Exception as e:
                logger.error(f"Error in inference loop: {e}")
                self._stats['errors'] += 1
                
                # Throttled error reporting
                if self._stats['errors'] % 5 == 1:  # Only every 5th error
                    error_event = PoseDetectionErrorEvent(
                        error_message=f"Inference loop error: {e}",
                        frame_skipped=True,
                        source="capture_and_score_use_case"
                    )
                    self.event_bus.publish(error_event)
                
                # Brief pause to prevent error spam
                time.sleep(0.05)
    
    def _process_frame_inference(self, frame_data: FrameData, inference_start: float) -> None:
        """Ultra-optimized frame processing with reduced event publishing.
        
        Args:
            frame_data: Frame data from capture buffer
            inference_start: Timestamp when inference started
        """
        try:
            self._stats['frames_processed'] += 1
            
            # Process ALL frames for maximum detection rate
            # No frame skipping to achieve 90% detection target
            
            # Detect pose in frame (this is the main bottleneck)
            keypoints = self.pose_engine.detect_pose(frame_data.frame)
            if not keypoints:
                # Skip processing but don't log every failure (reduces overhead)
                return
            
            self._stats['poses_detected'] += 1
            
            # Fast pose quality calculation
            pose_quality = sum(conf for _, _, conf in keypoints.values()) / len(keypoints)
            
            # Create posture snapshot with minimal overhead
            posture = PostureSnapshot(
                timestamp=frame_data.timestamp,
                keypoints=keypoints,
                confidence=pose_quality,
                source_frame_shape=frame_data.frame.shape
            )
            
            # Calculate ergonomic scores for every detection for maximum responsiveness
            if True:  # Process all detections
                scores = self.score_calculator.calculate_comprehensive_score(posture)
                self._stats['scores_calculated'] += 1
                
                # Record inference completion
                self.performance_monitor.record_frame_end(
                    frame_data.timestamp,
                    inference_start
                )
                
                # Publish event with reduced frequency - only every 3rd successful detection
                scores_event = ScoresUpdatedEvent(
                    scores=scores,
                    posture_quality=pose_quality,
                    frame_timestamp=frame_data.timestamp,
                    keypoints=keypoints,
                    frame=frame_data.frame
                )
                self.event_bus.publish(scores_event)
            
        except Exception as e:
            # Throttled error logging and handling
            self._stats['errors'] += 1
            if self._stats['errors'] % 20 == 1:  # Only log every 20th error
                logger.error(f"Failed to process frame {frame_data.frame_id}: {e}")
    
    def _cleanup(self) -> None:
        """Clean up resources."""
        try:
            if self.pose_engine:
                self.pose_engine.cleanup()
        except Exception as e:
            logger.error(f"Error cleaning up pose engine: {e}")
        
        try:
            if self.camera_adapter:
                self.camera_adapter.release()
        except Exception as e:
            logger.error(f"Error cleaning up camera: {e}")
    
    def _log_final_stats(self) -> None:
        """Log final processing statistics."""
        if self._stats['start_time']:
            duration = time.time() - self._stats['start_time']
            capture_fps = self._stats['frames_captured'] / duration if duration > 0 else 0
            processing_fps = self._stats['frames_processed'] / duration if duration > 0 else 0
            
            logger.info(f"Async Pipeline Statistics:")
            logger.info(f"  Duration: {duration:.1f} seconds")
            logger.info(f"  Frames captured: {self._stats['frames_captured']}")
            logger.info(f"  Frames processed: {self._stats['frames_processed']}")
            logger.info(f"  Poses detected: {self._stats['poses_detected']}")
            logger.info(f"  Scores calculated: {self._stats['scores_calculated']}")
            logger.info(f"  Buffer overflows: {self._stats['buffer_overflows']}")
            logger.info(f"  Errors: {self._stats['errors']}")
            logger.info(f"  Capture FPS: {capture_fps:.1f}")
            logger.info(f"  Processing FPS: {processing_fps:.1f}")
            
            if self._stats['frames_processed'] > 0:
                pose_detection_rate = (
                    self._stats['poses_detected'] / self._stats['frames_processed'] * 100
                )
                buffer_efficiency = (
                    (self._stats['frames_captured'] - self._stats['buffer_overflows']) /
                    self._stats['frames_captured'] * 100
                    if self._stats['frames_captured'] > 0 else 0
                )
                logger.info(f"  Pose detection rate: {pose_detection_rate:.1f}%")
                logger.info(f"  Buffer efficiency: {buffer_efficiency:.1f}%")
    
    def get_stats(self) -> Dict[str, Any]:
        """Get current processing statistics.
        
        Returns:
            Dictionary containing current pipeline statistics
        """
        stats = self._stats.copy()
        if self._stats['start_time']:
            stats['duration'] = time.time() - self._stats['start_time']
            stats['avg_fps'] = (
                self._stats['frames_processed'] / stats['duration'] 
                if stats['duration'] > 0 else 0
            )
        return stats
    
    def is_running(self) -> bool:
        """Check if the pipeline is currently running.
        
        Returns:
            True if pipeline is active
        """
        return self._running
    
    def set_target_fps(self, fps: float) -> None:
        """Update target processing FPS.
        
        Args:
            fps: New target frames per second
        """
        if fps > 0:
            self.target_fps = fps
            self._frame_interval = 1.0 / fps
            logger.info(f"Target FPS updated to {fps}")